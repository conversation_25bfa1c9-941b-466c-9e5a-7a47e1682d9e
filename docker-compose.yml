version: '3.8'

services:
  # Backend API service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend.fixed.new
      target: ${BACKEND_TARGET:-development}  # Use development or production target
    container_name: algotrader-backend
    volumes:
      - .:/app  # Mount code for development
      - /app/.pytest_cache  # Exclude pytest cache
      - /app/__pycache__  # Exclude Python cache
      - /app/frontend/node_modules  # Exclude frontend node_modules
      - /app/frontend/.next  # Exclude frontend build cache
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=********************************************/algotrader
      - TIMESCALE_URL=***********************************************/algotrader_timeseries
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - API_DEBUG=${API_DEBUG:-true}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - API_CORS_ORIGINS=${API_CORS_ORIGINS:-http://localhost:3000,http://frontend:3000,http://frontend-dev:3000}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - algotrader-network

  # Worker service for background tasks
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker.rebuild
    container_name: algotrader-worker
    volumes:
      - .:/app  # Mount code for development
      - /app/.pytest_cache  # Exclude pytest cache
      - /app/__pycache__  # Exclude Python cache
      - worker_logs:/app/logs  # Persist logs
      - worker_data:/app/data  # Persist data
    depends_on:
      postgres:
        condition: service_healthy
      timescaledb:
        condition: service_healthy
      redis:
        condition: service_healthy
    environment:
      - DATABASE_URL=********************************************/algotrader
      - TIMESCALE_URL=***********************************************/algotrader_timeseries
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - WORKER_CONCURRENCY=${WORKER_CONCURRENCY:-4}
      - WORKER_MAX_TASKS_PER_CHILD=${WORKER_MAX_TASKS_PER_CHILD:-50}
    healthcheck:
      test: ["CMD", "/usr/local/bin/worker_healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - algotrader-network

  # Celery beat service for scheduled tasks
  worker-beat:
    build:
      context: .
      dockerfile: Dockerfile.worker-beat.rebuild
    container_name: algotrader-worker-beat
    volumes:
      - .:/app  # Mount code for development
      - /app/.pytest_cache  # Exclude pytest cache
      - /app/__pycache__  # Exclude Python cache
      - worker_logs:/app/logs  # Persist logs
      - worker_data:/app/data  # Persist data
    depends_on:
      - worker
      - redis
    environment:
      - DATABASE_URL=********************************************/algotrader
      - TIMESCALE_URL=***********************************************/algotrader_timeseries
      - REDIS_URL=redis://redis:6379/0
      - ENVIRONMENT=${ENVIRONMENT:-development}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
    healthcheck:
      test: ["CMD", "/usr/local/bin/worker_beat_healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - algotrader-network

  # Flower service for Celery monitoring
  flower:
    build:
      context: .
      dockerfile: Dockerfile.flower
    container_name: algotrader-flower
    ports:
      - "5555:5555"
    depends_on:
      - worker
    environment:
      - REDIS_URL=redis://redis:6379/0
      - FLOWER_BASIC_AUTH=${FLOWER_BASIC_AUTH:-admin:admin}
      - FLOWER_URL_PREFIX=${FLOWER_URL_PREFIX:-}
      - FLOWER_PERSISTENT=${FLOWER_PERSISTENT:-False}
      - FLOWER_DB=/app/flower/flower.db
      - FLOWER_PURGE_OFFLINE_WORKERS=${FLOWER_PURGE_OFFLINE_WORKERS:-30}
      - FLOWER_STATE_SAVE_INTERVAL=${FLOWER_STATE_SAVE_INTERVAL:-30000}
      - FLOWER_MAX_WORKERS=${FLOWER_MAX_WORKERS:-10000}
      - FLOWER_MAX_TASKS=${FLOWER_MAX_TASKS:-10000}
      - FLOWER_INSPECT_TIMEOUT=${FLOWER_INSPECT_TIMEOUT:-1500}
    volumes:
      - flower_data:/app/flower:rw
    healthcheck:
      test: ["CMD", "/usr/local/bin/flower_healthcheck"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - algotrader-network

  # Frontend service for production
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: algotrader-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://backend:8000/api
      - NEXT_PUBLIC_APP_VERSION=${NEXT_PUBLIC_APP_VERSION:-0.1.0}
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - algotrader-network

  # Frontend development service - Production Level
  frontend-dev:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
      target: runtime
    container_name: algotrader-frontend-dev
    volumes:
      # Mount source code for hot-reloading
      - ./frontend:/app:cached
      # Preserve node_modules and build artifacts in named volumes for performance
      - frontend_dev_node_modules:/app/node_modules
      - frontend_dev_next:/app/.next
      # Mount configuration files for real-time updates
      - ./frontend/package.json:/app/package.json:ro
      - ./frontend/package-lock.json:/app/package-lock.json:ro
      - ./frontend/next.config.js:/app/next.config.js:ro
      - ./frontend/tsconfig.json:/app/tsconfig.json:ro
      - ./frontend/tailwind.config.js:/app/tailwind.config.js:ro
      - ./frontend/postcss.config.js:/app/postcss.config.js:ro
    ports:
      - "3000:3000"  # Development server
      - "9229:9229"  # Node.js debugging port
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://backend:8000/api
      - NEXT_PUBLIC_APP_VERSION=${NEXT_PUBLIC_APP_VERSION:-0.1.0-dev}
      - NEXT_PUBLIC_ENABLE_SW_IN_DEV=false
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
      - FAST_REFRESH=true
      - NODE_OPTIONS=--max-old-space-size=4096
      # Development debugging
      - DEBUG=${DEBUG:-false}
      - VERBOSE=${VERBOSE:-false}
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 15s
      retries: 5
      start_period: 90s
    restart: unless-stopped
    depends_on:
      - backend
    networks:
      - algotrader-network
    # Resource limits for development container
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Frontend testing service - Multi-stage testing environment
  frontend-test:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
      target: testing
    container_name: algotrader-frontend-test
    volumes:
      # Mount source code for testing
      - ./frontend:/app:cached
      # Preserve node_modules for performance
      - frontend_test_node_modules:/app/node_modules
      # Test results and coverage
      - ./frontend/coverage:/app/coverage
      - ./frontend/test-results:/app/test-results
    environment:
      - NODE_ENV=test
      - CI=true
    profiles:
      - testing
    networks:
      - algotrader-network

  # PostgreSQL database
  postgres:
    image: postgres:15-alpine
    container_name: algotrader-postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-algotrader}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - algotrader-network

  # TimescaleDB for time-series data
  timescaledb:
    image: timescale/timescaledb:latest-pg15
    container_name: algotrader-timescaledb
    volumes:
      - timescaledb_data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${TIMESCALE_DB:-algotrader_timeseries}
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - algotrader-network

  # Redis for caching and message broker
  redis:
    image: redis:7-alpine
    container_name: algotrader-redis
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass "${REDIS_PASSWORD:-}"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s
    restart: unless-stopped
    networks:
      - algotrader-network

  # PgAdmin for database management
  pgadmin:
    build:
      context: .
      dockerfile: Dockerfile.pgadmin
    container_name: algotrader-pgadmin
    ports:
      - "5050:80"
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_DEFAULT_EMAIL:-<EMAIL>}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_DEFAULT_PASSWORD:-admin}
      - PGADMIN_CONFIG_SERVER_MODE=False
      - PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED=False
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
      - timescaledb
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:80/misc/ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - algotrader-network

  # Storybook service for component development
  storybook:
    build:
      context: ./frontend
      dockerfile: Dockerfile.storybook
    container_name: algotrader-storybook
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "6006:6006"
    environment:
      - NODE_ENV=development
      - PORT=6006
      - DISABLE_PWA=true
    healthcheck:
      test: ["CMD", "/usr/local/bin/healthcheck.sh"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    networks:
      - algotrader-network

networks:
  algotrader-network:
    driver: bridge

volumes:
  postgres_data:
  timescaledb_data:
  redis_data:
  worker_logs:
  worker_data:
  pgadmin_data:
  flower_data:
  # Frontend development volumes for performance optimization
  frontend_dev_node_modules:
  frontend_dev_next:
  # Frontend testing volumes
  frontend_test_node_modules:


