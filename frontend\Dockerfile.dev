# AlgoTrader Frontend Development Container - Production Level Multi-Stage Build
# Comprehensive development environment with optimized multi-stage build approach
# Implements hot-reloading, isolated dependencies, and code management on local machine
# Last Updated: 2025-01-27

# ================================================================================================
# Stage 1: Base Stage - Common foundation with all dependencies
# ================================================================================================
FROM node:22.15.0-alpine AS base

# Install comprehensive system dependencies for all stages
RUN apk add --no-cache \
    libc6-compat \
    python3 \
    py3-pip \
    make \
    g++ \
    git \
    curl \
    wget \
    ca-certificates \
    bash \
    dumb-init \
    openssh-client \
    rsync \
    su-exec \
    tini \
    shadow \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Create non-root user for security (production-level approach)
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Create essential directories with proper permissions
RUN mkdir -p /app/.next /app/node_modules /app/coverage /app/test-results /app/logs && \
    chown -R nextjs:nodejs /app

# ================================================================================================
# Stage 2: Dependencies Stage - Isolated dependency management
# ================================================================================================
FROM base AS dependencies

# Copy package files for dependency installation (production-level caching)
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies with comprehensive error handling and optimization
RUN npm ci --legacy-peer-deps --no-audit --no-fund --verbose && \
    npm cache clean --force

# Install additional development tools and utilities for comprehensive development
RUN npm install --no-save --legacy-peer-deps \
    glob \
    cross-env \
    concurrently \
    nodemon \
    @types/node \
    typescript \
    webpack-dev-server \
    @next/bundle-analyzer

# Verify installation integrity and create dependency manifest
RUN npm ls --depth=0 > /app/dependency-manifest.txt || echo "Dependency verification completed with warnings" && \
    echo "Dependencies installed at: $(date)" >> /app/dependency-manifest.txt

# ================================================================================================
# Stage 3: Development Stage - Hot-reloading and development tools
# ================================================================================================
FROM base AS development

# Copy dependencies from isolated dependencies stage
COPY --from=dependencies --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=dependencies --chown=nextjs:nodejs /app/package*.json ./
COPY --from=dependencies --chown=nextjs:nodejs /app/dependency-manifest.txt ./

# Copy configuration files for optimal layer caching (production approach)
COPY --chown=nextjs:nodejs next.config.js ./
COPY --chown=nextjs:nodejs tailwind.config.js ./
COPY --chown=nextjs:nodejs postcss.config.js ./
COPY --chown=nextjs:nodejs eslint.config.js ./
COPY --chown=nextjs:nodejs jest.config.js ./
COPY --chown=nextjs:nodejs tsconfig.json ./

# ================================================================================================
# Stage 4: Production Build Stage (for testing and validation)
# ================================================================================================
FROM development AS builder

# Set production environment for build
ENV NODE_ENV=production

# Build the application for validation
RUN npm run build

# ================================================================================================
# Stage 5: Development Runtime (default stage)
# ================================================================================================
FROM development AS runtime

# Create comprehensive development scripts and health checks
RUN echo '#!/bin/bash\n\
# Comprehensive health check for development environment\n\
set -e\n\
\n\
# Function to check service health\n\
check_service() {\n\
    local url="$1"\n\
    local timeout="${2:-10}"\n\
    \n\
    if curl -f -s --max-time "$timeout" "$url" >/dev/null 2>&1; then\n\
        return 0\n\
    else\n\
        return 1\n\
    fi\n\
}\n\
\n\
# Check Next.js development server\n\
if check_service "http://localhost:${PORT:-3000}" 15; then\n\
    echo "✓ Next.js development server is healthy"\n\
    exit 0\n\
elif check_service "http://localhost:${PORT:-3000}/api/health" 10; then\n\
    echo "✓ API health endpoint is responding"\n\
    exit 0\n\
else\n\
    echo "✗ Health check failed - service not responding"\n\
    echo "Checking process status..."\n\
    ps aux | grep -E "(next|node)" | grep -v grep || echo "No Next.js processes found"\n\
    exit 1\n\
fi' > /usr/local/bin/healthcheck.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# Create enhanced development startup script with comprehensive functionality
RUN echo '#!/bin/bash\n\
# Production-level development startup script for AlgoTrader Frontend\n\
set -e\n\
\n\
# Logging function\n\
log() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"\n\
}\n\
\n\
log "=== AlgoTrader Frontend Development Environment ==="\n\
log "Node.js version: $(node --version)"\n\
log "NPM version: $(npm --version)"\n\
log "Environment: ${NODE_ENV:-development}"\n\
log "Port: ${PORT:-3000}"\n\
log "User: $(whoami)"\n\
log "Working Directory: $(pwd)"\n\
log "Memory limit: ${NODE_OPTIONS:-default}"\n\
log "================================================"\n\
\n\
# Ensure proper permissions\n\
if [ "$(whoami)" = "root" ]; then\n\
  log "Warning: Running as root. Switching to nextjs user..."\n\
  exec su-exec nextjs "$0" "$@"\n\
fi\n\
\n\
# Check and install dependencies if needed\n\
check_dependencies() {\n\
    if [ ! -d "node_modules" ] || [ ! -f "node_modules/.package-lock.json" ]; then\n\
        log "Installing/updating dependencies..."\n\
        if mountpoint -q node_modules 2>/dev/null; then\n\
            log "Detected node_modules as mounted volume - checking integrity..."\n\
            if [ ! -f "node_modules/.package-lock.json" ] || [ ! -d "node_modules/.bin" ]; then\n\
                log "Installing dependencies in mounted volume..."\n\
                npm install --legacy-peer-deps --no-audit --no-fund --verbose\n\
            else\n\
                log "Dependencies already installed in mounted volume"\n\
            fi\n\
        else\n\
            npm install --legacy-peer-deps --no-audit --no-fund --verbose\n\
        fi\n\
        log "Dependencies verification completed"\n\
    else\n\
        log "Dependencies already installed"\n\
    fi\n\
}\n\
\n\
# Setup symbolic links for case sensitivity\n\
setup_symlinks() {\n\
    if [ -f "create-symlinks.js" ]; then\n\
        log "Creating symbolic links for UI components..."\n\
        node create-symlinks.js || log "Warning: Symlink creation failed, continuing..."\n\
    fi\n\
}\n\
\n\
# Verify configuration files\n\
verify_config() {\n\
    if [ -f "next.config.js" ]; then\n\
        log "✓ Next.js configuration found"\n\
    else\n\
        log "⚠ Warning: next.config.js not found"\n\
    fi\n\
    \n\
    if [ -f "package.json" ]; then\n\
        log "✓ Package.json found"\n\
    else\n\
        log "✗ Error: package.json not found"\n\
        exit 1\n\
    fi\n\
}\n\
\n\
# Clear build cache\n\
clear_cache() {\n\
    if [ -d ".next" ]; then\n\
        log "Clearing Next.js cache..."\n\
        if mountpoint -q .next 2>/dev/null; then\n\
            log "Detected .next as mounted volume - clearing contents safely..."\n\
            find .next -mindepth 1 -maxdepth 1 -exec rm -rf {} + 2>/dev/null || true\n\
        else\n\
            log "Removing .next directory..."\n\
            rm -rf .next 2>/dev/null || true\n\
        fi\n\
    else\n\
        log "No existing .next cache found"\n\
    fi\n\
}\n\
\n\
# Signal handlers for graceful shutdown\n\
cleanup() {\n\
    log "Received shutdown signal, stopping development server..."\n\
    if [ ! -z "$PID" ]; then\n\
        kill -TERM $PID 2>/dev/null || true\n\
        wait $PID 2>/dev/null || true\n\
    fi\n\
    log "Development server stopped"\n\
    exit 0\n\
}\n\
\n\
trap cleanup TERM INT\n\
\n\
# Main execution\n\
check_dependencies\n\
setup_symlinks\n\
verify_config\n\
clear_cache\n\
\n\
log "Starting Next.js development server with hot-reloading..."\n\
log "Access the application at: http://localhost:${PORT:-3000}"\n\
log "Debug port available at: http://localhost:9229 (if debugging enabled)"\n\
log "================================================"\n\
\n\
# Start the development server\n\
npm run dev &\n\
PID=$!\n\
\n\
# Wait for the process\n\
wait $PID' > /usr/local/bin/start-dev.sh && \
    chmod +x /usr/local/bin/start-dev.sh

# Create enhanced debugging and development utilities script
RUN echo '#!/bin/bash\n\
# Development utilities and debugging tools for AlgoTrader Frontend\n\
set -e\n\
\n\
# Logging function\n\
log() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"\n\
}\n\
\n\
case "$1" in\n\
  "debug")\n\
    log "Starting development server in debug mode..."\n\
    log "Debug port: 9229"\n\
    NODE_OPTIONS="--inspect=0.0.0.0:9229 --max-old-space-size=4096" npm run dev\n\
    ;;\n\
  "test")\n\
    log "Running tests..."\n\
    npm run test\n\
    ;;\n\
  "test:watch")\n\
    log "Running tests in watch mode..."\n\
    npm run test:watch\n\
    ;;\n\
  "test:coverage")\n\
    log "Running tests with coverage..."\n\
    npm run test:coverage\n\
    ;;\n\
  "lint")\n\
    log "Running linter..."\n\
    npm run lint\n\
    ;;\n\
  "lint:fix")\n\
    log "Running linter with auto-fix..."\n\
    npm run lint -- --fix\n\
    ;;\n\
  "type-check")\n\
    log "Running TypeScript type check..."\n\
    npm run type-check\n\
    ;;\n\
  "build")\n\
    log "Building application..."\n\
    npm run build\n\
    ;;\n\
  "build:analyze")\n\
    log "Building application with bundle analysis..."\n\
    ANALYZE=true npm run build\n\
    ;;\n\
  "storybook")\n\
    log "Starting Storybook..."\n\
    npm run storybook\n\
    ;;\n\
  "clean")\n\
    log "Cleaning build artifacts..."\n\
    rm -rf .next node_modules/.cache coverage test-results\n\
    log "Build artifacts cleaned"\n\
    ;;\n\
  "clean:all")\n\
    log "Cleaning all artifacts including node_modules..."\n\
    rm -rf .next node_modules/.cache coverage test-results node_modules\n\
    log "All artifacts cleaned"\n\
    ;;\n\
  "install")\n\
    log "Installing dependencies..."\n\
    npm install --legacy-peer-deps --no-audit --no-fund\n\
    ;;\n\
  "update")\n\
    log "Updating dependencies..."\n\
    npm update --legacy-peer-deps\n\
    ;;\n\
  "audit")\n\
    log "Running security audit..."\n\
    npm audit\n\
    ;;\n\
  "info")\n\
    log "=== Development Environment Info ==="\n\
    echo "Node.js: $(node --version)"\n\
    echo "NPM: $(npm --version)"\n\
    echo "Working Directory: $(pwd)"\n\
    echo "User: $(whoami)"\n\
    echo "Environment: ${NODE_ENV:-development}"\n\
    echo "Port: ${PORT:-3000}"\n\
    echo "Memory Limit: ${NODE_OPTIONS:-default}"\n\
    if [ -f "package.json" ]; then\n\
        echo "Project: $(cat package.json | grep '"'"'"name"'"'"' | cut -d'"'"'":"'"'"' -f2 | tr -d '"'"' "'"'"')"\n\
        echo "Version: $(cat package.json | grep '"'"'"version"'"'"' | cut -d'"'"'":"'"'"' -f2 | tr -d '"'"' "'"'"')"\n\
    fi\n\
    ;;\n\
  "help"|*)\n\
    echo "=== AlgoTrader Frontend Development Tools ==="\n\
    echo "Available commands:"\n\
    echo "  debug        - Start with debugging enabled (port 9229)"\n\
    echo "  test         - Run tests"\n\
    echo "  test:watch   - Run tests in watch mode"\n\
    echo "  test:coverage - Run tests with coverage report"\n\
    echo "  lint         - Run linter"\n\
    echo "  lint:fix     - Run linter with auto-fix"\n\
    echo "  type-check   - Run TypeScript type checking"\n\
    echo "  build        - Build the application"\n\
    echo "  build:analyze - Build with bundle analysis"\n\
    echo "  storybook    - Start Storybook"\n\
    echo "  clean        - Clean build artifacts"\n\
    echo "  clean:all    - Clean all artifacts including node_modules"\n\
    echo "  install      - Install dependencies"\n\
    echo "  update       - Update dependencies"\n\
    echo "  audit        - Run security audit"\n\
    echo "  info         - Show environment information"\n\
    echo "  help         - Show this help"\n\
    echo "============================================"\n\
    ;;\n\
esac' > /usr/local/bin/dev-tools.sh && \
    chmod +x /usr/local/bin/dev-tools.sh

# Set comprehensive environment variables for development
ENV NODE_ENV=development
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED=1
ENV NEXT_PUBLIC_ENABLE_SW_IN_DEV=false

# File watching optimizations for hot-reloading
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true
ENV CHOKIDAR_INTERVAL=1000
ENV FAST_REFRESH=true

# Development-specific optimizations
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Performance and debugging
ENV FORCE_COLOR=1
ENV NPM_CONFIG_COLOR=always

# Create log directory and set permissions
RUN mkdir -p /app/logs && chown -R nextjs:nodejs /app/logs

# Expose ports for development server and debugging
EXPOSE 3000 9229

# Add comprehensive health check for development environment
HEALTHCHECK --interval=30s --timeout=15s --start-period=90s --retries=5 \
    CMD ["/usr/local/bin/healthcheck.sh"]

# Switch to non-root user for security
USER nextjs

# Use development startup script as default command
CMD ["/usr/local/bin/start-dev.sh"]

# ================================================================================================
# Stage 6: Testing Environment - Comprehensive testing setup
# ================================================================================================
FROM base AS testing

# Switch back to root for installing test dependencies
USER root

# Copy dependencies from dependencies stage
COPY --from=dependencies --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=dependencies --chown=nextjs:nodejs /app/package*.json ./

# Install additional testing tools and dependencies
RUN npm install --no-save --legacy-peer-deps \
    @playwright/test \
    jest-environment-jsdom \
    @testing-library/jest-dom \
    @testing-library/react \
    @testing-library/user-event \
    jest-axe \
    lighthouse \
    puppeteer

# Install Playwright browsers
RUN npx playwright install --with-deps

# Create comprehensive test runner script
RUN echo '#!/bin/bash\n\
# Comprehensive test runner for AlgoTrader Frontend\n\
set -e\n\
\n\
# Logging function\n\
log() {\n\
    echo "[$(date "+%Y-%m-%d %H:%M:%S")] $1"\n\
}\n\
\n\
case "$1" in\n\
  "unit")\n\
    log "Running unit tests..."\n\
    npm run test:unit\n\
    ;;\n\
  "integration")\n\
    log "Running integration tests..."\n\
    npm run test:integration\n\
    ;;\n\
  "e2e")\n\
    log "Running end-to-end tests..."\n\
    npm run test:e2e\n\
    ;;\n\
  "accessibility")\n\
    log "Running accessibility tests..."\n\
    npm run test:accessibility\n\
    ;;\n\
  "coverage")\n\
    log "Running tests with coverage..."\n\
    npm run test:coverage\n\
    ;;\n\
  "all")\n\
    log "Running all tests..."\n\
    npm run test:all\n\
    ;;\n\
  "ci")\n\
    log "Running CI tests..."\n\
    npm run test:ci\n\
    ;;\n\
  *)\n\
    log "Running default tests..."\n\
    npm run test\n\
    ;;\n\
esac' > /usr/local/bin/test-runner.sh && \
    chmod +x /usr/local/bin/test-runner.sh

# Set testing environment variables
ENV NODE_ENV=test
ENV CI=true

# Switch back to non-root user
USER nextjs

# Default command for testing stage
CMD ["/usr/local/bin/test-runner.sh"]

# ================================================================================================
# Stage 7: Production Stage - Optimized for security, performance, and minimal size
# ================================================================================================
FROM node:22.15.0-alpine AS production

# Install minimal runtime dependencies
RUN apk add --no-cache \
    libc6-compat \
    curl \
    ca-certificates \
    dumb-init \
    tini \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Create production health check script
RUN echo '#!/bin/sh\n\
# Production health check\n\
curl -f -s --max-time 10 http://localhost:${PORT:-3000}/api/health >/dev/null 2>&1 || \\\n\
curl -f -s --max-time 10 http://localhost:${PORT:-3000} >/dev/null 2>&1' > /usr/local/bin/healthcheck.sh && \
    chmod +x /usr/local/bin/healthcheck.sh

# Set production environment variables
ENV NODE_ENV=production
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"
ENV NEXT_TELEMETRY_DISABLED=1

# Expose port
EXPOSE 3000

# Add health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD ["/usr/local/bin/healthcheck.sh"]

# Switch to non-root user
USER nextjs

# Start the application
CMD ["dumb-init", "node", "server.js"]
