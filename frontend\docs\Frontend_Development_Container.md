# AlgoTrader Frontend Development Container

## Overview

The AlgoTrader Frontend Development Container is a production-level, multi-stage Docker container designed for comprehensive frontend development with hot-reloading, isolated dependencies, and optimized performance.

## Architecture

### Multi-Stage Build Approach

The container uses a sophisticated multi-stage build with the following stages:

1. **Base Stage**: Common foundation with all system dependencies
2. **Dependencies Stage**: Isolated dependency management
3. **Development Stage**: Hot-reloading and development tools
4. **Builder Stage**: Production build validation
5. **Runtime Stage**: Development runtime environment (default)
6. **Testing Stage**: Comprehensive testing environment
7. **Production Stage**: Optimized for security, performance, and minimal size

### Key Features

- **Hot-reloading** for rapid development
- **Optimized production** images for security and performance
- **Isolated dependencies** to prevent conflicts
- **Code management on local machine** with volume mounting
- **Comprehensive health checks** and monitoring
- **Multi-platform compatibility** for Windows development

## Container Specifications

### Base Configuration
- **Base Image**: `node:22.15.0-alpine`
- **Node.js Version**: 22.15.0
- **Package Manager**: npm with legacy peer deps support
- **User**: Non-root user (`nextjs:nodejs`) for security

### System Dependencies
- libc6-compat
- python3, py3-pip
- make, g++
- git, curl, wget
- ca-certificates
- bash, dumb-init
- openssh-client, rsync
- su-exec, tini
- shadow

### Development Tools
- TypeScript
- ESLint
- Jest
- Playwright (testing stage)
- Webpack Dev Server
- Bundle Analyzer

## Environment Variables

### Development Environment
```bash
NODE_ENV=development
PORT=3000
HOSTNAME=0.0.0.0
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_ENABLE_SW_IN_DEV=false
WATCHPACK_POLLING=true
CHOKIDAR_USEPOLLING=true
CHOKIDAR_INTERVAL=1000
FAST_REFRESH=true
NODE_OPTIONS=--max-old-space-size=4096
FORCE_COLOR=1
NPM_CONFIG_COLOR=always
```

### Production Environment
```bash
NODE_ENV=production
PORT=3000
HOSTNAME=0.0.0.0
NEXT_TELEMETRY_DISABLED=1
```

## Volume Mounts

### Development Volumes
- **Source Code**: `./frontend:/app:cached` - Local code mounted for hot-reloading
- **Node Modules**: `frontend_dev_node_modules:/app/node_modules` - Isolated dependencies
- **Build Cache**: `frontend_dev_next:/app/.next` - Build artifacts cache
- **Logs**: `frontend_dev_logs:/app/logs` - Application logs

## Exposed Ports

- **3000**: Next.js development server
- **9229**: Node.js debugging port

## Health Checks

### Development Health Check
- **Interval**: 30 seconds
- **Timeout**: 15 seconds
- **Start Period**: 90 seconds
- **Retries**: 5
- **Command**: Custom health check script

### Production Health Check
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Start Period**: 30 seconds
- **Retries**: 3
- **Command**: HTTP endpoint check

## Scripts and Utilities

### Development Startup Script (`/usr/local/bin/start-dev.sh`)
Comprehensive startup script with:
- Dependency verification and installation
- Symbolic link creation for case sensitivity
- Configuration validation
- Cache clearing
- Graceful shutdown handling
- Enhanced logging

### Development Tools Script (`/usr/local/bin/dev-tools.sh`)
Available commands:
- `debug` - Start with debugging enabled
- `test` - Run tests
- `test:watch` - Run tests in watch mode
- `test:coverage` - Run tests with coverage
- `lint` - Run linter
- `lint:fix` - Run linter with auto-fix
- `type-check` - Run TypeScript type checking
- `build` - Build the application
- `build:analyze` - Build with bundle analysis
- `storybook` - Start Storybook
- `clean` - Clean build artifacts
- `install` - Install dependencies
- `update` - Update dependencies
- `audit` - Run security audit
- `info` - Show environment information

### Health Check Script (`/usr/local/bin/healthcheck.sh`)
Comprehensive health monitoring with:
- Service availability checks
- API endpoint validation
- Process status monitoring
- Detailed error reporting

## Container Management

### Using the Management Script

The `dev-container-manager.sh` script provides comprehensive container management:

```bash
# Build the container
./dev-container-manager.sh build

# Start the development environment
./dev-container-manager.sh start

# View logs
./dev-container-manager.sh logs -f

# Open shell in container
./dev-container-manager.sh shell

# Run development tools
./dev-container-manager.sh exec dev-tools.sh debug

# Check container status
./dev-container-manager.sh status

# Stop and cleanup
./dev-container-manager.sh cleanup
```

### Using Docker Compose

```bash
# Build and start
docker-compose up -d frontend-dev

# View logs
docker-compose logs -f frontend-dev

# Execute commands
docker-compose exec frontend-dev npm run lint

# Stop
docker-compose stop frontend-dev
```

## Testing

### Container Testing Script

The `test-container.sh` script provides comprehensive testing:

```bash
# Run all tests
./test-container.sh test

# Test specific functionality
./test-container.sh startup
./test-container.sh health
./test-container.sh server
./test-container.sh reload
```

### Test Coverage
- Container startup validation
- Health check verification
- Next.js server responsiveness
- Hot-reloading functionality
- Volume mount validation
- Environment variable verification
- Command availability testing

## Development Workflow

### 1. Initial Setup
```bash
# Build the container
./dev-container-manager.sh build

# Start development environment
./dev-container-manager.sh start
```

### 2. Development
- Code changes are automatically detected via hot-reloading
- Access application at `http://localhost:3000`
- Debug at `http://localhost:9229` (when debugging enabled)

### 3. Testing
```bash
# Run tests in development container
./dev-container-manager.sh exec npm run test

# Run tests in dedicated testing container
./dev-container-manager.sh test unit true
```

### 4. Debugging
```bash
# Start with debugging enabled
./dev-container-manager.sh exec dev-tools.sh debug
```

## Troubleshooting

### Common Issues

1. **Container fails to start**
   - Check Docker daemon status
   - Verify port availability (3000, 9229)
   - Check system resources

2. **Hot-reloading not working**
   - Verify volume mounts
   - Check file permissions
   - Restart container

3. **Dependencies issues**
   - Clear node_modules volume
   - Rebuild container without cache
   - Check package.json for conflicts

4. **Performance issues**
   - Increase memory allocation
   - Check system resources
   - Optimize file watching settings

### Debugging Commands

```bash
# Check container logs
./dev-container-manager.sh logs

# Check container health
./dev-container-manager.sh status

# Run comprehensive tests
./test-container.sh test

# Access container shell
./dev-container-manager.sh shell
```

## Security Considerations

- Non-root user execution
- Minimal system dependencies
- Isolated dependency management
- Secure volume mounting
- Health check monitoring
- Resource limitations

## Performance Optimizations

- Multi-stage build for minimal image size
- Cached volume mounts for performance
- Optimized file watching for hot-reloading
- Memory allocation tuning
- Build artifact caching

## Integration

The frontend development container integrates seamlessly with:
- Backend API services
- Database containers
- Redis cache
- Testing frameworks
- CI/CD pipelines

## Maintenance

### Regular Tasks
- Update base image versions
- Security patch updates
- Dependency updates
- Performance monitoring
- Log rotation

### Monitoring
- Container health status
- Resource usage
- Application performance
- Error tracking
- Development metrics
