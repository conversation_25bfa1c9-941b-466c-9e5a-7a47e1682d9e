#!/bin/bash

# AlgoTrader Frontend Development Container Testing Script
# Comprehensive testing script for validating container functionality
# Last Updated: 2025-01-27

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="algotrader-frontend-dev"
SERVICE_NAME="frontend-dev"
TEST_TIMEOUT=300
HEALTH_CHECK_RETRIES=20
HEALTH_CHECK_INTERVAL=5

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test() {
    echo -e "${PURPLE}[TEST]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

# Test results tracking
TESTS_PASSED=0
TESTS_FAILED=0
FAILED_TESTS=()

# Test result functions
test_passed() {
    TESTS_PASSED=$((TESTS_PASSED + 1))
    log_success "✓ $1"
}

test_failed() {
    TESTS_FAILED=$((TESTS_FAILED + 1))
    FAILED_TESTS+=("$1")
    log_error "✗ $1"
}

# Check if Docker is running
check_docker() {
    log_step "Checking Docker status..."
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
    log_success "Docker is running"
}

# Check if container is running
container_running() {
    docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"
}

# Test container startup
test_container_startup() {
    log_test "Testing container startup..."
    
    cd ..
    
    # Stop container if running
    if container_running; then
        log_info "Stopping existing container..."
        docker-compose stop ${SERVICE_NAME} >/dev/null 2>&1
    fi
    
    # Start container
    log_info "Starting container..."
    if docker-compose up -d ${SERVICE_NAME} >/dev/null 2>&1; then
        test_passed "Container started successfully"
    else
        test_failed "Container failed to start"
        return 1
    fi
    
    # Wait for container to be running
    local retries=0
    while [ $retries -lt $HEALTH_CHECK_RETRIES ]; do
        if container_running; then
            test_passed "Container is running"
            return 0
        fi
        sleep $HEALTH_CHECK_INTERVAL
        retries=$((retries + 1))
    done
    
    test_failed "Container failed to start within timeout"
    return 1
}

# Test container health
test_container_health() {
    log_test "Testing container health..."
    
    local retries=0
    while [ $retries -lt $HEALTH_CHECK_RETRIES ]; do
        local health_status=$(docker inspect ${CONTAINER_NAME} --format='{{.State.Health.Status}}' 2>/dev/null || echo "unknown")
        
        case $health_status in
            "healthy")
                test_passed "Container is healthy"
                return 0
                ;;
            "unhealthy")
                test_failed "Container is unhealthy"
                return 1
                ;;
            "starting")
                log_info "Container health check is starting... (attempt $((retries + 1))/$HEALTH_CHECK_RETRIES)"
                ;;
            *)
                log_info "Container health status: $health_status (attempt $((retries + 1))/$HEALTH_CHECK_RETRIES)"
                ;;
        esac
        
        sleep $HEALTH_CHECK_INTERVAL
        retries=$((retries + 1))
    done
    
    test_failed "Container health check timeout"
    return 1
}

# Test Next.js development server
test_nextjs_server() {
    log_test "Testing Next.js development server..."
    
    local retries=0
    while [ $retries -lt $HEALTH_CHECK_RETRIES ]; do
        if curl -f -s --max-time 10 http://localhost:3000 >/dev/null 2>&1; then
            test_passed "Next.js development server is responding"
            return 0
        fi
        
        log_info "Waiting for Next.js server... (attempt $((retries + 1))/$HEALTH_CHECK_RETRIES)"
        sleep $HEALTH_CHECK_INTERVAL
        retries=$((retries + 1))
    done
    
    test_failed "Next.js development server is not responding"
    return 1
}

# Test hot-reloading functionality
test_hot_reloading() {
    log_test "Testing hot-reloading functionality..."
    
    # Create a test file
    local test_file="./frontend/test-hot-reload.txt"
    echo "test-$(date +%s)" > "$test_file"
    
    # Check if file appears in container
    sleep 2
    if docker exec ${CONTAINER_NAME} test -f "/app/test-hot-reload.txt" 2>/dev/null; then
        test_passed "File mounting is working"
        
        # Clean up test file
        rm -f "$test_file"
        
        # Verify file is removed from container
        sleep 2
        if ! docker exec ${CONTAINER_NAME} test -f "/app/test-hot-reload.txt" 2>/dev/null; then
            test_passed "Hot-reloading file sync is working"
        else
            test_failed "Hot-reloading file removal sync failed"
        fi
    else
        test_failed "File mounting is not working"
        rm -f "$test_file"
    fi
}

# Test container commands
test_container_commands() {
    log_test "Testing container commands..."
    
    # Test Node.js version
    if docker exec ${CONTAINER_NAME} node --version >/dev/null 2>&1; then
        local node_version=$(docker exec ${CONTAINER_NAME} node --version)
        test_passed "Node.js is available (version: $node_version)"
    else
        test_failed "Node.js is not available"
    fi
    
    # Test npm version
    if docker exec ${CONTAINER_NAME} npm --version >/dev/null 2>&1; then
        local npm_version=$(docker exec ${CONTAINER_NAME} npm --version)
        test_passed "npm is available (version: $npm_version)"
    else
        test_failed "npm is not available"
    fi
    
    # Test development tools
    if docker exec ${CONTAINER_NAME} /usr/local/bin/dev-tools.sh info >/dev/null 2>&1; then
        test_passed "Development tools are available"
    else
        test_failed "Development tools are not available"
    fi
}

# Test volume mounts
test_volume_mounts() {
    log_test "Testing volume mounts..."
    
    # Test source code mount
    if docker exec ${CONTAINER_NAME} test -f "/app/package.json" 2>/dev/null; then
        test_passed "Source code is mounted"
    else
        test_failed "Source code is not mounted"
    fi
    
    # Test node_modules volume
    if docker exec ${CONTAINER_NAME} test -d "/app/node_modules" 2>/dev/null; then
        test_passed "node_modules volume is mounted"
    else
        test_failed "node_modules volume is not mounted"
    fi
}

# Test environment variables
test_environment_variables() {
    log_test "Testing environment variables..."
    
    local node_env=$(docker exec ${CONTAINER_NAME} printenv NODE_ENV 2>/dev/null || echo "")
    if [ "$node_env" = "development" ]; then
        test_passed "NODE_ENV is set correctly"
    else
        test_failed "NODE_ENV is not set correctly (expected: development, got: $node_env)"
    fi
    
    local port=$(docker exec ${CONTAINER_NAME} printenv PORT 2>/dev/null || echo "")
    if [ "$port" = "3000" ]; then
        test_passed "PORT is set correctly"
    else
        test_failed "PORT is not set correctly (expected: 3000, got: $port)"
    fi
}

# Run all tests
run_all_tests() {
    log_info "=== AlgoTrader Frontend Development Container Tests ==="
    log_info "Starting comprehensive container testing..."
    echo ""
    
    # Initialize test results
    TESTS_PASSED=0
    TESTS_FAILED=0
    FAILED_TESTS=()
    
    # Run tests
    check_docker
    test_container_startup
    test_container_health
    test_nextjs_server
    test_hot_reloading
    test_container_commands
    test_volume_mounts
    test_environment_variables
    
    # Display results
    echo ""
    log_info "=== Test Results ==="
    log_success "Tests passed: $TESTS_PASSED"
    
    if [ $TESTS_FAILED -gt 0 ]; then
        log_error "Tests failed: $TESTS_FAILED"
        echo ""
        log_error "Failed tests:"
        for test in "${FAILED_TESTS[@]}"; do
            echo "  - $test"
        done
        echo ""
        log_error "Container testing failed. Please check the logs and fix the issues."
        return 1
    else
        echo ""
        log_success "All tests passed! Container is working correctly."
        return 0
    fi
}

# Show help
show_help() {
    echo "AlgoTrader Frontend Development Container Testing Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  test        Run all tests"
    echo "  startup     Test container startup only"
    echo "  health      Test container health only"
    echo "  server      Test Next.js server only"
    echo "  reload      Test hot-reloading only"
    echo "  commands    Test container commands only"
    echo "  volumes     Test volume mounts only"
    echo "  env         Test environment variables only"
    echo "  help        Show this help message"
    echo ""
}

# Main script logic
main() {
    case "${1:-test}" in
        "test")
            run_all_tests
            ;;
        "startup")
            check_docker
            test_container_startup
            ;;
        "health")
            test_container_health
            ;;
        "server")
            test_nextjs_server
            ;;
        "reload")
            test_hot_reloading
            ;;
        "commands")
            test_container_commands
            ;;
        "volumes")
            test_volume_mounts
            ;;
        "env")
            test_environment_variables
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
