#!/bin/bash

# AlgoTrader Frontend Development Container Manager
# Production-level script for managing the frontend development environment
# Last Updated: 2024-12-19

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="algotrader-frontend-dev"
SERVICE_NAME="frontend-dev"
COMPOSE_FILE="../docker-compose.yml"

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
check_docker() {
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Check if container exists
container_exists() {
    docker ps -a --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"
}

# Check if container is running
container_running() {
    docker ps --format "{{.Names}}" | grep -q "^${CONTAINER_NAME}$"
}

# Build the development container
build_container() {
    local target="${1:-runtime}"
    log_info "Building frontend development container (target: $target)..."
    cd ..
    if [ "$target" = "all" ]; then
        log_info "Building all stages..."
        docker-compose build --no-cache ${SERVICE_NAME}
        docker-compose build --no-cache frontend-test
        log_success "All container stages built successfully"
    else
        docker-compose build --no-cache ${SERVICE_NAME}
        log_success "Container built successfully (target: $target)"
    fi
}

# Start the development container
start_container() {
    log_info "Starting frontend development container..."
    cd ..
    docker-compose up -d ${SERVICE_NAME}

    # Wait for container to be healthy
    log_info "Waiting for container to be healthy..."
    timeout=120
    elapsed=0
    while [ $elapsed -lt $timeout ]; do
        if docker-compose ps ${SERVICE_NAME} | grep -q "healthy"; then
            log_success "Container is healthy and ready"
            return 0
        fi
        sleep 5
        elapsed=$((elapsed + 5))
        echo -n "."
    done

    log_warning "Container health check timeout. Check logs for issues."
}

# Stop the development container
stop_container() {
    log_info "Stopping frontend development container..."
    cd ..
    docker-compose stop ${SERVICE_NAME}
    log_success "Container stopped"
}

# Restart the development container
restart_container() {
    log_info "Restarting frontend development container..."
    stop_container
    start_container
}

# Show container logs
show_logs() {
    local follow_flag=""
    if [ "$1" = "-f" ] || [ "$1" = "--follow" ]; then
        follow_flag="-f"
    fi

    log_info "Showing container logs..."
    cd ..
    docker-compose logs $follow_flag ${SERVICE_NAME}
}

# Execute command in container
exec_command() {
    if ! container_running; then
        log_error "Container is not running. Start it first with: $0 start"
        exit 1
    fi

    log_info "Executing command in container: $*"
    docker exec -it ${CONTAINER_NAME} "$@"
}

# Open shell in container
open_shell() {
    if ! container_running; then
        log_error "Container is not running. Start it first with: $0 start"
        exit 1
    fi

    log_info "Opening shell in container..."
    docker exec -it ${CONTAINER_NAME} /bin/bash
}

# Show container status
show_status() {
    log_info "Container status:"
    cd ..
    docker-compose ps ${SERVICE_NAME}

    if container_running; then
        echo ""
        log_info "Container health:"
        docker inspect ${CONTAINER_NAME} --format='{{.State.Health.Status}}'

        echo ""
        log_info "Container resources:"
        docker stats ${CONTAINER_NAME} --no-stream
    fi
}

# Clean up development environment
cleanup() {
    log_info "Cleaning up development environment..."
    cd ..

    # Stop and remove container
    docker-compose down ${SERVICE_NAME}

    # Remove development volumes (optional)
    read -p "Remove development volumes (node_modules, .next)? [y/N]: " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume rm frontend_dev_node_modules frontend_dev_next 2>/dev/null || true
        log_success "Development volumes removed"
    fi

    log_success "Cleanup completed"
}

# Install dependencies
install_deps() {
    if ! container_running; then
        log_error "Container is not running. Start it first with: $0 start"
        exit 1
    fi

    log_info "Installing dependencies..."
    docker exec ${CONTAINER_NAME} npm install --legacy-peer-deps
    log_success "Dependencies installed"
}

# Run tests
run_tests() {
    local test_type="${1:-unit}"
    local use_test_container="${2:-false}"

    if [ "$use_test_container" = "true" ]; then
        log_info "Running $test_type tests in dedicated testing container..."
        cd ..
        docker-compose --profile testing run --rm frontend-test /usr/local/bin/test-runner.sh "$test_type"
    else
        if ! container_running; then
            log_error "Container is not running. Start it first with: $0 start"
            exit 1
        fi

        log_info "Running $test_type tests in development container..."
        docker exec ${CONTAINER_NAME} npm run test:$test_type
    fi
}

# Show help
show_help() {
    echo "AlgoTrader Frontend Development Container Manager"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build [target]  Build the development container"
    echo "                  Targets: runtime (default), all"
    echo "  start           Start the development container"
    echo "  stop            Stop the development container"
    echo "  restart         Restart the development container"
    echo "  logs [-f]       Show container logs (use -f to follow)"
    echo "  shell           Open shell in container"
    echo "  status          Show container status and health"
    echo "  exec <cmd>      Execute command in container"
    echo "  install         Install/update dependencies"
    echo "  test [type] [container]  Run tests (unit, integration, e2e)"
    echo "                  Use 'true' as second arg for dedicated test container"
    echo "  cleanup         Stop container and clean up volumes"
    echo "  help            Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build                    # Build the development container"
    echo "  $0 build all                # Build all container stages"
    echo "  $0 start                    # Start development server"
    echo "  $0 logs -f                  # Follow logs"
    echo "  $0 exec npm run lint        # Run linter"
    echo "  $0 test integration         # Run integration tests in dev container"
    echo "  $0 test unit true           # Run unit tests in dedicated test container"
    echo ""
}

# Main script logic
main() {
    check_docker

    case "${1:-help}" in
        "build")
            build_container "$2"
            ;;
        "start")
            start_container
            ;;
        "stop")
            stop_container
            ;;
        "restart")
            restart_container
            ;;
        "logs")
            show_logs "$2"
            ;;
        "shell")
            open_shell
            ;;
        "status")
            show_status
            ;;
        "exec")
            shift
            exec_command "$@"
            ;;
        "install")
            install_deps
            ;;
        "test")
            run_tests "$2" "$3"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# Run main function with all arguments
main "$@"
